# Main settings
COMPOSE_PROJECT_NAME=flarum-fpm
VIRTUAL_HOST=flarum.localhost
FASTCGI_PASS=php:9000
APP_PUBLIC_DIRECTORY=public

# MYSQL settings
MYSQL_ROOT_PASSWORD=root
MYSQL_HOST=db-${COMPOSE_PROJECT_NAME}
MYSQL_DATABASE=flarum
MYSQL_USER=flarum
MYSQL_PASSWORD=flarum

# GitHub settings
GITHUB_TOKEN=your_github_token_here

# Backup settings
BACKUP_DIR=backups
GPG_RECIPIENT=backup@flash
LOCAL_BACKUP_RETENTION_DAYS=21
RCLONE_REMOTE_1=webh:platforma:10
