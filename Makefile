# Makefile

# Load environment variables from .env file
ifneq (,$(wildcard ./.env))
    include .env
    export $(shell sed 's/=.*//' .env)
endif

# Variables
DOCKER_COMPOSE = docker compose
PHP_CONTAINER = php

# Default target: list all available targets
.PHONY: help
help: ## Display this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Connect to PHP container
.PHONY: sh
sh: ## Connect to PHP container
	@$(DOCKER_COMPOSE) exec $(PHP_CONTAINER) /bin/bash

# Stop all services
.PHONY: stop
stop: ## Stop all services
	@$(DOCKER_COMPOSE) stop

# Run all services
.PHONY: up
up: ## Run all services
	@$(DOCKER_COMPOSE) up -d

# Bring down all services
.PHONY: down
down: ## Bring down all services
	@$(DOCKER_COMPOSE) down

# Restart all services
.PHONY: restart
restart: ## Restart all services
	@$(DOCKER_COMPOSE) restart

# Build all services
.PHONY: build
build: ## Build all services
	@$(DOCKER_COMPOSE) build
