services:

  # Nginx Service
  nginx:
    build:
      context: .
      dockerfile: docker/nginx/Dockerfile
    environment:
      VIRTUAL_HOST: ${VIRTUAL_HOST}
      LETSENCRYPT_HOST: ${VIRTUAL_HOST}
      FASTCGI_PASS: ${FASTCGI_PASS}
      APP_PUBLIC_DIRECTORY: ${APP_PUBLIC_DIRECTORY}
    expose:
      - 80
    hostname: nginx-${COMPOSE_PROJECT_NAME}
    container_name: nginx-${COMPOSE_PROJECT_NAME}
    volumes:
        - type: volume
          source: flarum
          target: /var/www/app
          read_only: true
          volume:
            subpath: ${APP_PUBLIC_DIRECTORY}
    restart: unless-stopped
    networks:
      - nginx-proxy
      - default
    depends_on:
      - php

  # Flarum Service
  php:
    build:
      context: .
      dockerfile: docker/php/Dockerfile
    restart: unless-stopped
    hostname: php
    container_name: php-${COMPOSE_PROJECT_NAME}
    environment:
      GITHUB_TOKEN: ${GITHUB_TOKEN}
    labels:
      - "ofelia.enabled=true"
      - "ofelia.job-exec.flarum-cron-job-${COMPOSE_PROJECT_NAME}.schedule=@every 5m"
      - "ofelia.job-exec.flarum-cron-job-${COMPOSE_PROJECT_NAME}.user=www-data"
      - "ofelia.job-exec.flarum-cron-job-${COMPOSE_PROJECT_NAME}.command=php /var/www/app/flarum schedule:run"
    working_dir: /var/www/app
    volumes:
      - flarum:/var/www/app
    depends_on:
      - db
    networks:
      - default

# DB Service
  db:
    image: mariadb:11.5
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    working_dir: /var/lib/mysql
    volumes:
      - db:/var/lib/mysql
    hostname: db
    container_name: db-${COMPOSE_PROJECT_NAME}
    networks:
      - default

volumes:
  flarum:
  db:

networks:
  nginx-proxy:
    name: nginx-proxy
    external: true
