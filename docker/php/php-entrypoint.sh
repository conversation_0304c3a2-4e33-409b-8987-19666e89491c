#!/bin/bash
set -e

# If no command was passed, proceed to initial boot-up and run PHP-FPM, otherwise execute the command.
if [[ -z "$@" ]]; then

  cd /var/www/app
  export COMPOSER_CACHE_DIR=/tmp

  # Configure GitHub token for Composer if provided
  if [[ -n "$GITHUB_TOKEN" ]]; then
    echo "Configuring Composer with GitHub token"
    composer config -g github-oauth.github.com "$GITHUB_TOKEN"
  fi

  if [[ ! -f "composer.json" ]]; then
    echo "Installing Flarum"
    composer create-project flarum/flarum .
  else
    echo "Installing dependencies"
    composer install
  fi

  composer clear-cache

  echo "Running PHP-FPM"
  exec php-fpm
else
  exec "$@"
fi
